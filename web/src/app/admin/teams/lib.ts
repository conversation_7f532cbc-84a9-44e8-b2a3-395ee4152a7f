import { TeamArgs } from "./types";

export const createTeam = async (teamArgs: TeamArgs) => {
  return fetch("/api/manage/user-teams", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(teamArgs),
  });
};

export const updateTeam = async (teamId: number, teamArgs: TeamArgs) => {
  return fetch(`/api/manage/user-teams/${teamId}`, {
    method: "PATCH",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(teamArgs),
  });
};

export const deleteTeam = async (teamId: number) => {
  return fetch(`/api/manage/user-teams/${teamId}`, {
    method: "DELETE",
  });
};

