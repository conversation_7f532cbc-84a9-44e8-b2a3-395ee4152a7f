from typing import cast

from onyx.auth.schemas import User<PERSON><PERSON>
from onyx.auth.schemas import UserStatus
from onyx.configs.constants import KV_USER_STORE_KEY
from onyx.key_value_store.factory import get_kv_store
from onyx.key_value_store.interface import KvKeyNotFoundError
from onyx.utils.special_types import JSON_ro


def get_invited_users() -> list[str]:
    """Legacy function to get invited users as email list for backward compatibility"""
    try:
        store = get_kv_store()
        data = store.load(KV_USER_STORE_KEY)

        # Handle both old format (list of strings) and new format (list of dicts)
        if isinstance(data, list) and len(data) > 0:
            if isinstance(data[0], str):
                # Old format: list of email strings
                return cast(list, data)
            elif isinstance(data[0], dict):
                # New format: list of {email, role} dicts
                return [user_data["email"] for user_data in data]

        return cast(list, data) if isinstance(data, list) else []
    except KvKeyNotFoundError:
        return list()


def get_invited_users_with_roles() -> list[dict[str, str]]:
    """Get invited users with their roles"""
    try:
        store = get_kv_store()
        data = store.load(KV_USER_STORE_KEY)

        # Handle both old format (list of strings) and new format (list of dicts)
        if isinstance(data, list) and len(data) > 0:
            if isinstance(data[0], str):
                # Old format: convert to new format with default role
                return [{"email": email, "role": UserRole.BASIC.value} for email in data]
            elif isinstance(data[0], dict):
                # New format: return as is
                return cast(list, data)

        return cast(list, data) if isinstance(data, list) else []
    except KvKeyNotFoundError:
        return list()


def write_invited_users(emails: list[str]) -> int:
    """Legacy function to write invited users as email list for backward compatibility"""
    store = get_kv_store()
    store.store(KV_USER_STORE_KEY, cast(JSON_ro, emails))
    return len(emails)


def write_invited_users_with_roles(users: list[dict[str, str]]) -> int:
    """Write invited users with their roles"""
    store = get_kv_store()
    store.store(KV_USER_STORE_KEY, cast(JSON_ro, users))
    return len(users)


def get_invited_users_with_status() -> list[dict[str, str]]:
    """Get invited users with their roles and status"""
    try:
        store = get_kv_store()
        data = store.load(KV_USER_STORE_KEY)

        # Handle different formats
        if isinstance(data, list) and len(data) > 0:
            if isinstance(data[0], str):
                # Old format: convert to new format with default role and status
                return [
                    {
                        "email": email,
                        "role": UserRole.BASIC.value,
                        "status": UserStatus.INVITED_BASIC.value
                    }
                    for email in data
                ]
            elif isinstance(data[0], dict):
                # Check if status field exists, if not add it based on role
                result = []
                for user_data in data:
                    if "status" not in user_data:
                        # Determine status based on role
                        role = user_data.get("role", UserRole.BASIC.value)
                        if role == UserRole.ADMIN.value:
                            user_data["status"] = UserStatus.INVITED_ADMIN.value
                        else:
                            user_data["status"] = UserStatus.INVITED_BASIC.value
                    result.append(user_data)
                return result

        return cast(list, data) if isinstance(data, list) else []
    except KvKeyNotFoundError:
        return list()


def write_invited_users_with_status(users: list[dict[str, str]]) -> int:
    """Write invited users with their roles and status"""
    store = get_kv_store()
    store.store(KV_USER_STORE_KEY, cast(JSON_ro, users))
    return len(users)


def determine_initial_status(role: UserRole) -> UserStatus:
    """Determine the initial status for a newly invited user based on their role"""
    if role == UserRole.ADMIN:
        return UserStatus.INVITED_ADMIN
    else:
        return UserStatus.INVITED_BASIC


def update_user_status_in_invites(email: str, new_status: UserStatus) -> bool:
    """Update the status of an invited user"""
    try:
        invited_users = get_invited_users_with_status()

        for user_data in invited_users:
            if user_data["email"] == email:
                user_data["status"] = new_status.value
                write_invited_users_with_status(invited_users)
                return True

        return False  # User not found in invites
    except Exception:
        return False
