import uuid
from enum import Enum

from fastapi_users import schemas


class UserRole(str, Enum):
    """
    User roles
    - Basic can't perform any admin actions
    - Admin can perform all admin actions
    - Curator can perform admin actions for
        groups they are curators of
    - Global Curator can perform admin actions
        for all groups they are a member of
    - Limited can access a limited set of basic api endpoints
    - Slack are users that have used onyx via slack but dont have a web login
    - External permissioned users that have been picked up during the external permissions sync process but don't have a web login
    """

    LIMITED = "limited"
    BASIC = "basic"
    ADMIN = "admin"
    CURATOR = "curator"
    GLOBAL_CURATOR = "global_curator"
    SLACK_USER = "slack_user"
    EXT_PERM_USER = "ext_perm_user"

    def is_web_login(self) -> bool:
        return self not in [
            UserRole.SLACK_USER,
            UserRole.EXT_PERM_USER,
        ]


class UserStatus(str, Enum):
    """
    User invitation and signup status
    - invited_admin: Invited admin user who can sign up immediately
    - invited_basic: Invited basic user who cannot sign up yet (needs group assignment)
    - pending_assignment: Basic user still waiting to be assigned to a team
    - ready_to_signup: Basic user is now assigned to a group, so can sign up
    - active: User has completed signup/onboarding
    - inactive: User access revoked
    """

    INVITED_ADMIN = "invited_admin"
    INVITED_BASIC = "invited_basic"
    PENDING_ASSIGNMENT = "pending_assignment"
    READY_TO_SIGNUP = "ready_to_signup"
    ACTIVE = "active"
    INACTIVE = "inactive"

    def can_signup(self) -> bool:
        """Returns True if user can proceed with signup"""
        return self in [
            UserStatus.INVITED_ADMIN,
            UserStatus.READY_TO_SIGNUP,
        ]

    def is_invited(self) -> bool:
        """Returns True if user is in an invited state"""
        return self in [
            UserStatus.INVITED_ADMIN,
            UserStatus.INVITED_BASIC,
            UserStatus.PENDING_ASSIGNMENT,
            UserStatus.READY_TO_SIGNUP,
        ]


class UserRead(schemas.BaseUser[uuid.UUID]):
    role: UserRole


class UserCreate(schemas.BaseUserCreate):
    role: UserRole = UserRole.BASIC
    tenant_id: str | None = None


class UserUpdateWithRole(schemas.BaseUserUpdate):
    role: UserRole


class UserUpdate(schemas.BaseUserUpdate):
    """
    Role updates are not allowed through the user update endpoint for security reasons
    Role changes should be handled through a separate, admin-only process
    """


class AuthBackend(str, Enum):
    REDIS = "redis"
    POSTGRES = "postgres"
