from typing import Generic
from typing import <PERSON><PERSON>
from typing import TypeVar
from uuid import <PERSON>UID

from pydantic import BaseModel

from onyx.auth.schemas import UserRole
from onyx.auth.schemas import UserStatus
from onyx.db.models import User


DataT = TypeVar("DataT")


class StatusResponse(BaseModel, Generic[DataT]):
    success: bool
    message: Optional[str] = None
    data: Optional[DataT] = None


class ApiKey(BaseModel):
    api_key: str


class IdReturn(BaseModel):
    id: int


class MinimalUserSnapshot(BaseModel):
    id: UUID
    email: str

class UserIdSnapshot(BaseModel):
    id: UUID

class FullUserSnapshot(BaseModel):
    id: UUID
    email: str
    role: UserRole
    is_active: bool
    password_configured: bool
    user_status: UserStatus

    @classmethod
    def from_user_model(cls, user: User) -> "FullUserSnapshot":
        # Default to ACTIVE status for existing users without status
        status = user.user_status if user.user_status is not None else UserStatus.ACTIVE
        return cls(
            id=user.id,
            email=user.email,
            role=user.role,
            is_active=user.is_active,
            password_configured=user.password_configured,
            user_status=status,
        )


class InvitedUserSnapshot(BaseModel):
    email: str
    role: UserRole | None = None
    status: UserStatus | None = None


class DisplayPriorityRequest(BaseModel):
    display_priority_map: dict[int, int]
