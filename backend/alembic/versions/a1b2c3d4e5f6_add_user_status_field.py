"""add user_status field

Revision ID: a1b2c3d4e5f6
Revises: dfbe9e93d3c7
Create Date: 2025-01-14 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "a1b2c3d4e5f6"
down_revision = "dfbe9e93d3c7"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add user_status column with default value 'active'
    op.add_column(
        "user",
        sa.Column(
            "user_status",
            sa.Enum(
                "invited_admin",
                "invited_basic", 
                "pending_assignment",
                "ready_to_signup",
                "active",
                "inactive",
                name="userstatus",
                native_enum=False
            ),
            nullable=False,
            server_default="active"
        ),
    )


def downgrade() -> None:
    op.drop_column("user", "user_status")
    # Drop the enum type
    op.execute("DROP TYPE IF EXISTS userstatus")
